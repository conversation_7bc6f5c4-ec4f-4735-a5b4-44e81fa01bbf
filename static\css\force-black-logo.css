/**
 * Force Black Logo Color - Final Override
 * This file must be loaded LAST to override all other logo styles
 */

/* Force black color for logo in all possible states and selectors */
.navbar-logo,
.navbar-logo:link,
.navbar-logo:visited,
.navbar-logo:hover,
.navbar-logo:focus,
.navbar-logo:active,
.logo,
.logo:link,
.logo:visited,
.logo:hover,
.logo:focus,
.logo:active,
a.navbar-logo,
a.logo,
.dark-theme .navbar-logo,
.dark-theme .navbar-logo:link,
.dark-theme .navbar-logo:visited,
.dark-theme .navbar-logo:hover,
.dark-theme .navbar-logo:focus,
.dark-theme .navbar-logo:active,
.dark-theme .logo,
.dark-theme .logo:link,
.dark-theme .logo:visited,
.dark-theme .logo:hover,
.dark-theme .logo:focus,
.dark-theme .logo:active,
.dark-theme a.navbar-logo,
.dark-theme a.logo {
    color: #000000 !important;
    background: none !important;
    background-image: none !important;
    background-color: transparent !important;
    background-clip: initial !important;
    -webkit-background-clip: initial !important;
    -moz-background-clip: initial !important;
    text-shadow: none !important;
    filter: none !important;
}

/* Remove any pseudo-elements that might add color */
.navbar-logo::before,
.navbar-logo::after,
.logo::before,
.logo::after,
.dark-theme .navbar-logo::before,
.dark-theme .navbar-logo::after,
.dark-theme .logo::before,
.dark-theme .logo::after {
    display: none !important;
    content: none !important;
}

/* Force black on mobile */
@media (max-width: 768px) {
    .navbar-logo,
    .navbar-logo:hover,
    .logo,
    .logo:hover,
    .dark-theme .navbar-logo,
    .dark-theme .navbar-logo:hover,
    .dark-theme .logo,
    .dark-theme .logo:hover {
        color: #000000 !important;
        background: none !important;
        background-image: none !important;
    }
}
