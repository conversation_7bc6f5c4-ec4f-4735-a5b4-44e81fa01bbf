{% extends "base.html" %}

{% block title %}Ziantrix - AI Co-Workers for Your Business - No Tech Team Needed{% endblock %}

{% block meta_description %}Ziantrix builds custom chatbots and smart AI agents that handle support, meetings, and ops - all built for low-cost CPU deployment.{% endblock %}

{% block header_content %}
<section class="hero-section">
    <div class="hero-bg">
        <div class="hero-shape hero-shape-1"></div>
        <div class="hero-shape hero-shape-2"></div>
        <div class="hero-shape hero-shape-3"></div>
    </div>

    <div class="hero-content" style="text-align: center;">
        <h1 class="hero-title" style="text-align: center;">AI Co-Workers for Your Business - No Tech Team Needed.</h1>
        <p class="hero-subtitle" style="text-align: center;">Ziantrix builds custom chatbots and smart AI agents that handle support, meetings, and ops - all built for low-cost CPU deployment.</p>

        <div class="hero-cta" style="display: flex; justify-content: center; align-items: stretch; gap: 1rem; flex-wrap: nowrap; max-width: 800px; margin: 0 auto;">
            <a href="javascript:void(0);" onclick="openDemoModal()" class="btn btn-primary" style="background: #3b82f6 !important; background-image: none !important; color: white !important; flex: 1; max-width: 400px; padding: 12px 24px; font-size: 0.9rem; text-align: center; white-space: normal; line-height: 1.2; border-radius: 8px; text-decoration: none; display: flex; align-items: center; justify-content: center;">🚀 Slash Support Costs — Launch Pilot Now</a>
            <a href="{{ calendly_link or 'https://calendly.com/vijay-kodam98/demo-call-with-ziantrix' }}" target="_blank" rel="noopener noreferrer preconnect" class="btn btn-secondary optimized-calendar-link" style="background: #8b5cf6 !important; background-image: none !important; color: white !important; flex: 1; max-width: 400px; padding: 12px 24px; font-size: 0.9rem; text-align: center; white-space: normal; line-height: 1.2; border-radius: 8px; text-decoration: none; display: flex; align-items: center; justify-content: center;">📞 Book a Free Demo — Limited Slots Available</a>
        </div>

        <div class="hero-video">
            <video autoplay muted loop playsinline poster="{{ url_for('static', filename='img/video-poster.webp') }}" data-critical>
                <source src="{{ url_for('static', filename='video/ziantrix-demo.mp4') }}" type="video/mp4">
                Your browser does not support the video tag.
            </video>
            <div class="video-play-button" id="heroVideoPlay">
                <i class="fas fa-play"></i>
            </div>
        </div>
    </div>
</section>


{% endblock %}

{% block content %}
<!-- Inline style with !important to override all other styles -->
<style>
    /* Direct targeting with !important to override everything */
    .feature-metric,
    .feature-metric-label,
    .feature-proof-point,
    .feature-card .feature-metric,
    .feature-card .feature-metric-label,
    .feature-card .feature-proof-point,
    .feature-card [class*="metric"],
    .feature-card *[class*="metric"],
    div.feature-metric,
    p.feature-metric-label,
    p.feature-proof-point,
    .feature-card div.feature-metric,
    .feature-card p.feature-metric-label,
    .feature-card p.feature-proof-point {
        background-color: transparent !important;
        background: transparent !important;
        background-image: none !important;
        box-shadow: none !important;
        border: none !important;
        padding: 0 !important;
    }

    /* Target the second feature card specifically */
    .feature-card:nth-child(2) .feature-metric,
    .feature-card:nth-child(2) .feature-metric-label,
    .feature-card:nth-child(2) .feature-proof-point,
    .feature-card:nth-child(2) *,
    .feature-card:nth-child(2) [class*="metric"],
    .feature-card:nth-child(2) *[class*="metric"] {
        background-color: transparent !important;
        background: transparent !important;
        background-image: none !important;
        box-shadow: none !important;
        border: none !important;
        padding: 0 !important;
    }

    /* Hero button fixes */
    .hero-cta .btn {
        background-image: none !important;
        min-height: 50px !important;
        border: none !important;
        font-weight: 600 !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.3s ease !important;
    }

    .hero-cta .btn.btn-primary {
        background: #3b82f6 !important;
        background-color: #3b82f6 !important;
    }

    .hero-cta .btn.btn-secondary {
        background: #8b5cf6 !important;
        background-color: #8b5cf6 !important;
    }

    .hero-cta .btn:hover {
        transform: scale(1.02) !important;
    }

    .hero-cta .btn.btn-primary:hover {
        background: #2563eb !important;
        background-color: #2563eb !important;
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
    }

    .hero-cta .btn.btn-secondary:hover {
        background: #7c3aed !important;
        background-color: #7c3aed !important;
        box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4) !important;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .hero-cta {
            flex-direction: row !important;
            gap: 0.5rem !important;
        }

        .hero-cta .btn {
            font-size: 0.8rem !important;
            padding: 10px 16px !important;
            max-width: 45% !important;
        }
    }

    @media (max-width: 480px) {
        .hero-cta .btn {
            font-size: 0.75rem !important;
            padding: 8px 12px !important;
            max-width: 48% !important;
        }
    }

    .hero-title,
    .section-title,
    .section-header h2, /* Target h2 within section-header */
    .feature-title,
    .service-title,
    .solution-title,
    .mission-statement h3,
    .ai-demo-card h3,
    .use-case-preview h4,
    .testimonial-author-name {
        color: #3b82f6 !important; /* Apply the requested blue color */
    }

    /* Ensure dark theme titles are also blue */
    .dark-theme .hero-title,
    .dark-theme .section-title,
    .dark-theme .section-header h2, /* Target h2 within dark theme section-header */
    .dark-theme .feature-title,
    .dark-theme .service-title,
    .dark-theme .solution-title,
    .dark-theme .mission-statement h3,
    .dark-theme .ai-demo-card h3,
    .dark-theme .use-case-preview h4,
    .dark-theme .testimonial-author-name {
        color: #60a5fa !important; /* A lighter blue for dark theme */
    }

    /* Specific overrides if needed (e.g., for elements with strong background colors) */
    /* Example: If a title is on a dark background and needs white text */
    .service-header h3 {
        color: white !important;
    }

    .experience-ai-section .section-title, /* Keep text shadow */
    .experience-ai-section .section-header h2 {
        color: #3b82f6 !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .dark-theme .experience-ai-section .section-title,
    .dark-theme .experience-ai-section .section-header h2 {
        color: #60a5fa !important;
    }

    /* Explicitly target specific section titles */
    #services .section-header h2,
    #solutions .section-header h2,
    #impact .section-header h2, /* What Our Clients Say */
    #faq .section-header h2 /* Frequently Asked Questions */
     {
        color: #3b82f6 !important;
    }

    /* Explicitly target specific section titles in dark theme */
    .dark-theme #services .section-header h2,
    .dark-theme #solutions .section-header h2,
    .dark-theme #impact .section-header h2,
    .dark-theme #faq .section-header h2
     {
        color: #60a5fa !important;
    }
</style>

<script>
    // Immediate execution script to remove backgrounds from metrics
    (function() {
        function removeAllBackgrounds() {
            // Target all elements with these classes
            var elements = document.querySelectorAll('.feature-metric, .feature-metric-label, .feature-proof-point');
            for (var i = 0; i < elements.length; i++) {
                elements[i].style.backgroundColor = 'transparent';
                elements[i].style.background = 'transparent';
                elements[i].style.boxShadow = 'none';
                elements[i].style.border = 'none';
                elements[i].style.padding = '0';
            }

            // Target the second feature card specifically
            var featureCards = document.querySelectorAll('.feature-card');
            if (featureCards.length > 1) {
                var secondCard = featureCards[1];
                var secondCardElements = secondCard.querySelectorAll('*');
                for (var j = 0; j < secondCardElements.length; j++) {
                    secondCardElements[j].style.backgroundColor = 'transparent';
                    secondCardElements[j].style.background = 'transparent';
                }
            }
        }

        // Run immediately
        removeAllBackgrounds();

        // Also run when DOM is loaded
        document.addEventListener('DOMContentLoaded', removeAllBackgrounds);

        // Run after a delay
        setTimeout(removeAllBackgrounds, 100);
        setTimeout(removeAllBackgrounds, 500);
    })();
</script>

<style>
    /* Remove all backgrounds from feature metrics */
    .feature-card .feature-metric,
    .feature-card .feature-metric-label,
    .feature-card .feature-proof-point,
    .feature-card .metric-wrapper,
    .feature-card [class*="metric"],
    .feature-card *[class*="metric"] {
        background-color: transparent !important;
        background: transparent !important;
        background-image: none !important;
        box-shadow: none !important;
        border: none !important;
        padding: 0 !important;
    }

    /* Target the second feature card specifically */
    .feature-card:nth-child(2) .feature-metric,
    .feature-card:nth-child(2) .feature-metric-label,
    .feature-card:nth-child(2) .feature-proof-point {
        background-color: transparent !important;
        background: transparent !important;
    }

    /* Services Section Styles */
    .services-section {
        padding: 80px 0;
        background-color: #f8f9fa;
    }

    .dark-theme .services-section {
        background-color: #1a202c;
    }

    .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }

    .service-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .service-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    }

    .dark-theme .service-card {
        background-color: #2d3748;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    }

    .service-header {
        padding: 20px;
        background-color: #1976d2;
        color: white;
        display: flex;
        align-items: center;
    }

    .dark-theme .service-header {
        background-color: #2196f3;
    }

    .service-icon {
        font-size: 2rem;
        margin-right: 15px;
    }

    .service-header h3 {
        margin: 0;
        font-size: 1.5rem;
    }

    .service-content {
        padding: 20px;
    }

    .service-content h4 {
        color: #1976d2;
        margin-top: 0;
        margin-bottom: 15px;
        font-size: 1.2rem;
    }

    .dark-theme .service-content h4 {
        color: #64b5f6;
    }

    .service-content p {
        color: #666;
        margin-bottom: 20px;
    }

    .dark-theme .service-content p {
        color: #a0aec0;
    }

    .service-features {
        list-style: none;
        padding: 0;
        margin-bottom: 25px;
    }

    .service-features li {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }

    .service-features li i {
        color: #1976d2;
        margin-right: 10px;
    }

    .dark-theme .service-features li i {
        color: #64b5f6;
    }

    .service-card .btn {
        display: inline-block;
        padding: 10px 20px;
        background-color: #1976d2;
        color: white;
        text-decoration: none;
        border-radius: 4px;
        transition: background-color 0.3s ease;
    }

    .service-card .btn:hover {
        background-color: #1565c0;
    }

    .dark-theme .service-card .btn {
        background-color: #2196f3;
    }

    .dark-theme .service-card .btn:hover {
        background-color: #1e88e5;
    }

    @media (max-width: 768px) {
        .services-grid {
            grid-template-columns: 1fr;
        }
    }
</style>


<!-- Why Ziantrix Section (Merged Features + About) -->
<section id="features" class="features-section section-spacing">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Why Ziantrix</h2>
            <p class="section-description">Smarter AI. Lower Costs. Built for Growing Businesses.</p>
        </div>

        <!-- Mission Statement -->
        <div class="mission-statement" style="text-align: center; max-width: 800px; margin: 0 auto 3rem; padding: 2rem; background: rgba(79, 70, 229, 0.05); border-radius: 12px;">
            <h3 style="color: var(--color-primary); margin-bottom: 1rem;">What Makes Us Different</h3>
            <p style="font-size: 1.1rem; line-height: 1.6;">Ziantrix delivers intelligent AI chatbots that feel human — without needing expensive hardware. Our platform is optimized to run on regular systems, which helps businesses save money and launch faster.</p>
        </div>

        <div class="features-grid" style="grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem;">
            <!-- 24/7 AI Support -->
            <div class="feature-card">
                <div class="feature-icon">
                    <span style="font-size: 2rem;">⚡</span>
                </div>
                <h3 class="feature-title">24/7 AI Support</h3>
                <p class="feature-description">Always available to respond to customer questions, any time of day.</p>
                <div class="feature-metric">99.9%</div>
                <p class="feature-metric-label">Uptime</p>
                <p class="feature-proof-point">Your support is always online, backed by real-time performance monitoring.</p>
            </div>

            <!-- Text + Voice in 40+ Languages -->
            <div class="feature-card">
                <div class="feature-icon">
                    <span style="font-size: 2rem;">🌍</span>
                </div>
                <h3 class="feature-title">Text + Voice in 40+ Languages</h3>
                <p class="feature-description">Whether it's chat or voice, Ziantrix speaks your customer's language — across popular platforms like websites, WhatsApp, and apps.</p>
                <div class="feature-metric">More Language Options</div>
                <p class="feature-metric-label"></p>
                <p class="feature-proof-point">Twice as many supported languages compared to many competitors.</p>
            </div>

            <!-- Easy Integration With Your Tools -->
            <div class="feature-card">
                <div class="feature-icon">
                    <span style="font-size: 2rem;">🔄</span>
                </div>
                <h3 class="feature-title">Easy Integration With Your Tools</h3>
                <p class="feature-description">Ziantrix works with your existing CRM, helpdesk, or internal systems. Setup is simple and handled by our team.</p>
                <div class="feature-metric">Quick Setup</div>
                <p class="feature-metric-label"></p>
                <p class="feature-proof-point">Go live in just 2–4 weeks, with all features ready to use from day one.</p>
            </div>

            <!-- Runs Efficiently on Standard Devices -->
            <div class="feature-card">
                <div class="feature-icon">
                    <span style="font-size: 2rem;">💻</span>
                </div>
                <h3 class="feature-title">Runs Efficiently on Standard Devices</h3>
                <p class="feature-description">No need for expensive servers or GPUs. Ziantrix is built to work smoothly on basic business infrastructure.</p>
                <div class="feature-metric">Up to 65%</div>
                <p class="feature-metric-label">Less Resource Usage</p>
                <p class="feature-proof-point">Uses significantly less processing power without compromising quality.</p>
            </div>
        </div>

        <!-- Consolidated Metrics Dashboard -->
        <div class="metrics-dashboard" style="margin-top: 3rem; padding: 2rem; background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(168, 85, 247, 0.1)); border-radius: 12px;">
            <h3 style="text-align: center; margin-bottom: 2rem; color: var(--color-primary);">Trusted Across Industries</h3>
            <p style="text-align: center; margin-bottom: 2rem; font-size: 1.1rem;">We help businesses reduce workload, improve service, and get faster results.</p>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 2rem; text-align: center;">
                <div>
                    <div style="font-size: 2.5rem; font-weight: bold; color: var(--color-primary);">70%</div>
                    <div style="font-weight: 600;">Lower Support Costs</div>
                    <div style="font-size: 0.9rem; opacity: 0.8;">From reduced ticket volume and faster resolutions.</div>
                </div>
                <div>
                    <div style="font-size: 2.5rem; font-weight: bold; color: var(--color-primary);">85%</div>
                    <div style="font-weight: 600;">Routine Task Automation</div>
                    <div style="font-size: 0.9rem; opacity: 0.8;">Most common customer queries are handled automatically.</div>
                </div>
                <div>
                    <div style="font-size: 2.5rem; font-weight: bold; color: var(--color-primary);">96%</div>
                    <div style="font-weight: 600;">Customer Satisfaction</div>
                    <div style="font-size: 0.9rem; opacity: 0.8;">Based on average client feedback.</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section - Tagged to services-nav-link -->
<section id="services" class="services-section section-spacing" data-nav-target="services-nav-link">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Our Services</h2>
            <p class="section-description">AI-powered solutions for your business</p>
        </div>
        <div class="services-grid" style="display: grid !important; grid-template-columns: repeat(3, 1fr) !important; gap: 30px !important; width: 100% !important; max-width: 1200px !important; margin: 0 auto !important; position: relative !important; z-index: 1 !important;">
            <!-- Chatbot Services -->
            <div class="service-card" style="width: 100% !important; max-width: none !important; min-width: 0 !important; box-sizing: border-box !important; height: 520px !important; min-height: 520px !important; max-height: 520px !important; display: flex !important; flex-direction: column !important; position: relative !important; padding-bottom: 120px !important;">
                <div class="service-card-header">
                    <div class="service-icon">
                        <span style="font-size: 2.5rem; color: var(--color-primary);">🤖</span>
                    </div>
                    <div class="service-header-text">
                        <h3 class="service-title">Chatbot Services</h3>
                        <p class="service-subtitle">Advanced Conversational AI</p>
                    </div>
                </div>
                <div class="service-card-body">
                    <ul class="service-features-list">
                        <li>
                            <span class="feature-icon"><i class="fas fa-language"></i></span>
                            <span class="feature-text">Natural language processing</span>
                        </li>
                        <li>
                            <span class="feature-icon"><i class="fas fa-comments"></i></span>
                            <span class="feature-text">Multi-channel deployment</span>
                        </li>
                        <li>
                            <span class="feature-icon"><i class="fas fa-database"></i></span>
                            <span class="feature-text">Custom knowledge base integration</span>
                        </li>
                        <li>
                            <span class="feature-icon"><i class="fas fa-chart-line"></i></span>
                            <span class="feature-text">Analytics and reporting</span>
                        </li>
                    </ul>
                </div>
                <div class="service-card-footer" style="width: 100% !important; max-width: 100% !important; min-width: 0 !important; box-sizing: border-box !important; padding: 0 24px 24px !important; position: absolute !important; bottom: 30px !important; left: 0 !important; right: 0 !important;">
                    <a href="javascript:void(0);" onclick="openDemoModal()" class="service-cta-button" style="width: 100% !important; max-width: 100% !important; min-width: 0 !important; box-sizing: border-box !important; display: flex !important; justify-content: center !important; align-items: center !important; padding: 14px 20px !important; text-align: center !important; white-space: normal !important; overflow: visible !important; text-overflow: clip !important; word-break: normal !important; word-wrap: normal !important; hyphens: none !important; line-height: 1.2 !important; min-height: 48px !important; height: auto !important; font-size: 1rem !important;">Automate 85% of Support Tickets Now</a>
                </div>
            </div>

            <!-- AI Services -->
            <div class="service-card" style="width: 100% !important; max-width: none !important; min-width: 0 !important; box-sizing: border-box !important; height: 520px !important; min-height: 520px !important; max-height: 520px !important; display: flex !important; flex-direction: column !important; position: relative !important; padding-bottom: 120px !important;">
                <div class="service-card-header">
                    <div class="service-icon">
                        <span style="font-size: 2.5rem; color: var(--color-primary);">🧠</span>
                    </div>
                    <div class="service-header-text">
                        <h3 class="service-title">AI Services</h3>
                        <p class="service-subtitle">Custom AI Solutions</p>
                    </div>
                </div>
                <div class="service-card-body">
                    <ul class="service-features-list">
                        <li>
                            <span class="feature-icon"><i class="fas fa-chart-bar"></i></span>
                            <span class="feature-text">Predictive analytics</span>
                        </li>
                        <li>
                            <span class="feature-icon"><i class="fas fa-cogs"></i></span>
                            <span class="feature-text">Machine learning models</span>
                        </li>
                        <li>
                            <span class="feature-icon"><i class="fas fa-file-alt"></i></span>
                            <span class="feature-text">Data processing and analysis</span>
                        </li>
                        <li>
                            <span class="feature-icon"><i class="fas fa-lightbulb"></i></span>
                            <span class="feature-text">AI strategy consulting</span>
                        </li>
                    </ul>
                </div>
                <div class="service-card-footer" style="width: 100% !important; max-width: 100% !important; min-width: 0 !important; box-sizing: border-box !important; padding: 0 24px 24px !important; position: absolute !important; bottom: 30px !important; left: 0 !important; right: 0 !important;">
                    <a href="javascript:void(0);" onclick="openDemoModal()" class="service-cta-button" style="width: 100% !important; max-width: 100% !important; min-width: 0 !important; box-sizing: border-box !important; display: flex !important; justify-content: center !important; align-items: center !important; padding: 14px 20px !important; text-align: center !important; white-space: normal !important; overflow: visible !important; text-overflow: clip !important; word-break: normal !important; word-wrap: normal !important; hyphens: none !important; line-height: 1.2 !important; min-height: 48px !important; height: auto !important; font-size: 1rem !important;">Transform Customer Experience in 14 Days</a>
                </div>
            </div>

            <!-- AI Agents -->
            <div class="service-card" style="width: 100% !important; max-width: none !important; min-width: 0 !important; box-sizing: border-box !important; height: 520px !important; min-height: 520px !important; max-height: 520px !important; display: flex !important; flex-direction: column !important; position: relative !important; padding-bottom: 120px !important;">
                <div class="service-card-header">
                    <div class="service-icon">
                        <span style="font-size: 2.5rem; color: var(--color-primary);">⚡</span>
                    </div>
                    <div class="service-header-text">
                        <h3 class="service-title">AI Agents</h3>
                        <p class="service-subtitle">Intelligent Automation</p>
                    </div>
                </div>
                <div class="service-card-body">
                    <ul class="service-features-list">
                        <li>
                            <span class="feature-icon"><i class="fas fa-robot"></i></span>
                            <span class="feature-text">Task automation</span>
                        </li>
                        <li>
                            <span class="feature-icon"><i class="fas fa-sitemap"></i></span>
                            <span class="feature-text">Decision-making capabilities</span>
                        </li>
                        <li>
                            <span class="feature-icon"><i class="fas fa-sync-alt"></i></span>
                            <span class="feature-text">Process optimization</span>
                        </li>
                        <li>
                            <span class="feature-icon"><i class="fas fa-graduation-cap"></i></span>
                            <span class="feature-text">Continuous learning</span>
                        </li>
                    </ul>
                </div>
                <div class="service-card-footer" style="width: 100% !important; max-width: 100% !important; min-width: 0 !important; box-sizing: border-box !important; padding: 0 24px 24px !important; position: absolute !important; bottom: 30px !important; left: 0 !important; right: 0 !important;">
                    <a href="javascript:void(0);" onclick="openDemoModal()" class="service-cta-button" style="width: 100% !important; max-width: 100% !important; min-width: 0 !important; box-sizing: border-box !important; display: flex !important; justify-content: center !important; align-items: center !important; padding: 14px 20px !important; text-align: center !important; white-space: normal !important; overflow: visible !important; text-overflow: clip !important; word-break: normal !important; word-wrap: normal !important; hyphens: none !important; line-height: 1.2 !important; min-height: 48px !important; height: auto !important; font-size: 1rem !important;">Triple Your Team's Efficiency Today</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Industry Solutions -->
<section id="solutions" class="industry-solutions section-spacing">
    <div class="section-header">
        <h2>Solutions for Every Industry</h2>
        <p>AI Solutions Tailored to Your Industry — Built to Deliver Real Outcomes</p>
    </div>

    <div class="solutions-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 1.25rem; max-width: 1200px; margin: 0 auto;">
        <!-- E-commerce Solution -->
        <div class="solution-card" style="background: var(--color-background); border-radius: 8px; padding: 1.25rem; box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08); border: 1px solid var(--color-border, #e2e8f0); height: auto; min-height: 260px;">
            <div class="solution-icon" style="width: 50px; height: 50px; background: linear-gradient(135deg, #4f46e5, #a855f7); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-bottom: 1rem;">
                <i class="fas fa-shopping-cart" style="color: white; font-size: 20px;"></i>
            </div>
            <h3 class="solution-title" style="color: var(--color-primary); margin-bottom: 0.5rem; font-size: 1.2rem; font-weight: 600;">E-commerce</h3>
            <p class="solution-description" style="color: var(--color-text); margin-bottom: 1rem; font-size: 0.9rem; line-height: 1.4;">Smarter shopping assistants designed to improve the customer journey</p>
            <ul class="solution-benefits" style="list-style: none; padding: 0; margin: 0;">
                <li style="display: flex; align-items: flex-start; margin-bottom: 0.5rem; font-size: 0.85rem; line-height: 1.3;"><span class="check-icon" style="color: #10b981; margin-right: 0.5rem; font-weight: bold;">✓</span> <span class="benefit-text" style="color: var(--color-text);">Built to reduce cart abandonment with timely nudges and support</span></li>
                <li style="display: flex; align-items: flex-start; margin-bottom: 0.5rem; font-size: 0.85rem; line-height: 1.3;"><span class="check-icon" style="color: #10b981; margin-right: 0.5rem; font-weight: bold;">✓</span> <span class="benefit-text" style="color: var(--color-text);">Personalized product suggestions based on customer behavior</span></li>
                <li style="display: flex; align-items: flex-start; margin-bottom: 0.5rem; font-size: 0.85rem; line-height: 1.3;"><span class="check-icon" style="color: #10b981; margin-right: 0.5rem; font-weight: bold;">✓</span> <span class="benefit-text" style="color: var(--color-text);">24/7 order tracking to reduce repetitive support requests</span></li>
            </ul>
        </div>

        <!-- Financial Services Solution -->
        <div class="solution-card" style="background: var(--color-background); border-radius: 8px; padding: 1.25rem; box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08); border: 1px solid var(--color-border, #e2e8f0); height: auto; min-height: 260px;">
            <div class="solution-icon" style="width: 50px; height: 50px; background: linear-gradient(135deg, #10b981, #06d6a0); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-bottom: 1rem;">
                <i class="fas fa-university" style="color: white; font-size: 20px;"></i>
            </div>
            <h3 class="solution-title" style="color: var(--color-primary); margin-bottom: 0.5rem; font-size: 1.2rem; font-weight: 600;">Financial Services</h3>
            <p class="solution-description" style="color: var(--color-text); margin-bottom: 1rem; font-size: 0.9rem; line-height: 1.4;">Secure, AI-powered support that scales with your compliance needs</p>
            <ul class="solution-benefits" style="list-style: none; padding: 0; margin: 0;">
                <li style="display: flex; align-items: flex-start; margin-bottom: 0.5rem; font-size: 0.85rem; line-height: 1.3;"><span class="check-icon" style="color: #10b981; margin-right: 0.5rem; font-weight: bold;">✓</span> <span class="benefit-text" style="color: var(--color-text);">Designed with GDPR & PCI-DSS readiness in mind</span></li>
                <li style="display: flex; align-items: flex-start; margin-bottom: 0.5rem; font-size: 0.85rem; line-height: 1.3;"><span class="check-icon" style="color: #10b981; margin-right: 0.5rem; font-weight: bold;">✓</span> <span class="benefit-text" style="color: var(--color-text);">Automates routine banking queries to reduce support volume</span></li>
                <li style="display: flex; align-items: flex-start; margin-bottom: 0.5rem; font-size: 0.85rem; line-height: 1.3;"><span class="check-icon" style="color: #10b981; margin-right: 0.5rem; font-weight: bold;">✓</span> <span class="benefit-text" style="color: var(--color-text);">Built-in fraud alert triggers for early issue detection</span></li>
            </ul>
        </div>

        <!-- Healthcare Solution -->
        <div class="solution-card" style="background: var(--color-background); border-radius: 8px; padding: 1.25rem; box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08); border: 1px solid var(--color-border, #e2e8f0); height: auto; min-height: 260px;">
            <div class="solution-icon" style="width: 50px; height: 50px; background: linear-gradient(135deg, #f59e0b, #f97316); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-bottom: 1rem;">
                <i class="fas fa-hospital" style="color: white; font-size: 20px;"></i>
            </div>
            <h3 class="solution-title" style="color: var(--color-primary); margin-bottom: 0.5rem; font-size: 1.2rem; font-weight: 600;">Healthcare</h3>
            <p class="solution-description" style="color: var(--color-text); margin-bottom: 1rem; font-size: 0.9rem; line-height: 1.4;">AI that supports patient engagement, securely and efficiently</p>
            <ul class="solution-benefits" style="list-style: none; padding: 0; margin: 0;">
                <li style="display: flex; align-items: flex-start; margin-bottom: 0.5rem; font-size: 0.85rem; line-height: 1.3;"><span class="check-icon" style="color: #10b981; margin-right: 0.5rem; font-weight: bold;">✓</span> <span class="benefit-text" style="color: var(--color-text);">Appointment reminders designed to lower no-show rates</span></li>
                <li style="display: flex; align-items: flex-start; margin-bottom: 0.5rem; font-size: 0.85rem; line-height: 1.3;"><span class="check-icon" style="color: #10b981; margin-right: 0.5rem; font-weight: bold;">✓</span> <span class="benefit-text" style="color: var(--color-text);">Triage-style interactions to guide patients toward appropriate care</span></li>
                <li style="display: flex; align-items: flex-start; margin-bottom: 0.5rem; font-size: 0.85rem; line-height: 1.3;"><span class="check-icon" style="color: #10b981; margin-right: 0.5rem; font-weight: bold;">✓</span> <span class="benefit-text" style="color: var(--color-text);">Developed with HIPAA-aligned design principles</span></li>
            </ul>
        </div>

        <!-- Education Solution -->
        <div class="solution-card" style="background: var(--color-background); border-radius: 8px; padding: 1.25rem; box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08); border: 1px solid var(--color-border, #e2e8f0); height: auto; min-height: 260px;">
            <div class="solution-icon" style="width: 50px; height: 50px; background: linear-gradient(135deg, #8b5cf6, #a855f7); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-bottom: 1rem;">
                <i class="fas fa-graduation-cap" style="color: white; font-size: 20px;"></i>
            </div>
            <h3 class="solution-title" style="color: var(--color-primary); margin-bottom: 0.5rem; font-size: 1.2rem; font-weight: 600;">Education</h3>
            <p class="solution-description" style="color: var(--color-text); margin-bottom: 1rem; font-size: 0.9rem; line-height: 1.4;">24/7 AI support to assist both students and administrative staff</p>
            <ul class="solution-benefits" style="list-style: none; padding: 0; margin: 0;">
                <li style="display: flex; align-items: flex-start; margin-bottom: 0.5rem; font-size: 0.85rem; line-height: 1.3;"><span class="check-icon" style="color: #10b981; margin-right: 0.5rem; font-weight: bold;">✓</span> <span class="benefit-text" style="color: var(--color-text);">On-demand Q&A engine for academic and support queries</span></li>
                <li style="display: flex; align-items: flex-start; margin-bottom: 0.5rem; font-size: 0.85rem; line-height: 1.3;"><span class="check-icon" style="color: #10b981; margin-right: 0.5rem; font-weight: bold;">✓</span> <span class="benefit-text" style="color: var(--color-text);">Streamlines administrative tasks like schedules and deadlines</span></li>
                <li style="display: flex; align-items: flex-start; margin-bottom: 0.5rem; font-size: 0.85rem; line-height: 1.3;"><span class="check-icon" style="color: #10b981; margin-right: 0.5rem; font-weight: bold;">✓</span> <span class="benefit-text" style="color: var(--color-text);">Multilingual capabilities to support diverse learners</span></li>
            </ul>
        </div>

        <!-- HR Solution -->
        <div class="solution-card" style="background: var(--color-background); border-radius: 8px; padding: 1.25rem; box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08); border: 1px solid var(--color-border, #e2e8f0); height: auto; min-height: 260px;">
            <div class="solution-icon" style="width: 50px; height: 50px; background: linear-gradient(135deg, #ef4444, #dc2626); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-bottom: 1rem;">
                <i class="fas fa-user-tie" style="color: white; font-size: 20px;"></i>
            </div>
            <h3 class="solution-title" style="color: var(--color-primary); margin-bottom: 0.5rem; font-size: 1.2rem; font-weight: 600;">Human Resources</h3>
            <p class="solution-description" style="color: var(--color-text); margin-bottom: 1rem; font-size: 0.9rem; line-height: 1.4;">Intelligent automation for faster HR service and employee onboarding</p>
            <ul class="solution-benefits" style="list-style: none; padding: 0; margin: 0;">
                <li style="display: flex; align-items: flex-start; margin-bottom: 0.5rem; font-size: 0.85rem; line-height: 1.3;"><span class="check-icon" style="color: #10b981; margin-right: 0.5rem; font-weight: bold;">✓</span> <span class="benefit-text" style="color: var(--color-text);">Digital onboarding assistants to shorten ramp-up time</span></li>
                <li style="display: flex; align-items: flex-start; margin-bottom: 0.5rem; font-size: 0.85rem; line-height: 1.3;"><span class="check-icon" style="color: #10b981; margin-right: 0.5rem; font-weight: bold;">✓</span> <span class="benefit-text" style="color: var(--color-text);">AI-driven responses for policy and benefits questions</span></li>
                <li style="display: flex; align-items: flex-start; margin-bottom: 0.5rem; font-size: 0.85rem; line-height: 1.3;"><span class="check-icon" style="color: #10b981; margin-right: 0.5rem; font-weight: bold;">✓</span> <span class="benefit-text" style="color: var(--color-text);">Designed to improve internal support efficiency</span></li>
            </ul>
        </div>

        <!-- Legal Solution -->
        <div class="solution-card" style="background: var(--color-background); border-radius: 8px; padding: 1.25rem; box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08); border: 1px solid var(--color-border, #e2e8f0); height: auto; min-height: 260px;">
            <div class="solution-icon" style="width: 50px; height: 50px; background: linear-gradient(135deg, #06b6d4, #0891b2); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-bottom: 1rem;">
                <i class="fas fa-balance-scale" style="color: white; font-size: 20px;"></i>
            </div>
            <h3 class="solution-title" style="color: var(--color-primary); margin-bottom: 0.5rem; font-size: 1.2rem; font-weight: 600;">Legal</h3>
            <p class="solution-description" style="color: var(--color-text); margin-bottom: 1rem; font-size: 0.9rem; line-height: 1.4;">Support tools to streamline legal operations and document handling</p>
            <ul class="solution-benefits" style="list-style: none; padding: 0; margin: 0;">
                <li style="display: flex; align-items: flex-start; margin-bottom: 0.5rem; font-size: 0.85rem; line-height: 1.3;"><span class="check-icon" style="color: #10b981; margin-right: 0.5rem; font-weight: bold;">✓</span> <span class="benefit-text" style="color: var(--color-text);">Automated assistance for standard contracts and FAQs</span></li>
                <li style="display: flex; align-items: flex-start; margin-bottom: 0.5rem; font-size: 0.85rem; line-height: 1.3;"><span class="check-icon" style="color: #10b981; margin-right: 0.5rem; font-weight: bold;">✓</span> <span class="benefit-text" style="color: var(--color-text);">AI-driven compliance alerts and regulatory guidance</span></li>
                <li style="display: flex; align-items: flex-start; margin-bottom: 0.5rem; font-size: 0.85rem; line-height: 1.3;"><span class="check-icon" style="color: #10b981; margin-right: 0.5rem; font-weight: bold;">✓</span> <span class="benefit-text" style="color: var(--color-text);">Document prep support to improve speed and consistency</span></li>
            </ul>
        </div>
    </div>
</section>

<!-- Experience Our AI Section -->
<section id="experience-ai" class="experience-ai-section section-spacing" style="background: linear-gradient(135deg, rgba(79, 70, 229, 0.02), rgba(168, 85, 247, 0.02)); position: relative; overflow: hidden;">
    <!-- Background pattern for better content visibility -->
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 20% 80%, rgba(79, 70, 229, 0.05) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.05) 0%, transparent 50%); pointer-events: none;"></div>

    <div class="container" style="max-width: 1000px; margin: 0 auto; padding: 0 2rem; position: relative; z-index: 1;">
        <div class="section-header">
            <h2 class="section-title" style="color: var(--color-primary); text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">Experience Our AI</h2>
            <p class="section-description" style="color: var(--color-text); font-weight: 500;">Coming Soon: See Ziantrix in Action</p>
        </div>

        <div style="margin-top: 2rem; margin-bottom: 3rem; text-align: center;">
            <p style="font-size: 1.1rem; color: var(--color-text); line-height: 1.6; max-width: 800px; margin: 0 auto; background: rgba(255, 255, 255, 0.8); padding: 1.5rem; border-radius: 12px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);">
                We're building an immersive experience to demonstrate how Ziantrix's CPU-efficient AI performs in real-world business scenarios. Here's what's on the way:
            </p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 2rem; margin-top: 2rem; max-width: 900px; margin-left: auto; margin-right: auto;">
            <!-- Live Chat Simulator -->
            <div class="ai-demo-card" style="background: rgba(255, 255, 255, 0.95); border-radius: 12px; padding: 1.8rem; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12); border: 1px solid rgba(79, 70, 229, 0.1); backdrop-filter: blur(10px);">
                <div style="display: flex; align-items: center; margin-bottom: 1.2rem;">
                    <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #4f46e5, #a855f7); border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 1rem; box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);">
                        <span style="color: white; font-size: 18px;">🔄</span>
                    </div>
                    <div>
                        <h3 style="margin: 0; color: var(--color-primary); font-size: 1.1rem; font-weight: 600;">Live Chat Simulator (Coming Soon)</h3>
                    </div>
                </div>
                <div style="margin-bottom: 1rem;">
                    <p style="font-size: 0.95rem; color: var(--color-text); line-height: 1.5; margin-bottom: 0.8rem;">
                        Test drive our AI with real-time, human-like responses. Engage with sample queries and see how our chatbot handles them intelligently across industries.
                    </p>
                </div>
            </div>

            <!-- Interactive Demo -->
            <div class="ai-demo-card" style="background: rgba(255, 255, 255, 0.95); border-radius: 12px; padding: 1.8rem; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12); border: 1px solid rgba(16, 185, 129, 0.1); backdrop-filter: blur(10px);">
                <div style="display: flex; align-items: center; margin-bottom: 1.2rem;">
                    <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #10b981, #06d6a0); border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 1rem; box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);">
                        <span style="color: white; font-size: 18px;">🧪</span>
                    </div>
                    <div>
                        <h3 style="margin: 0; color: var(--color-primary); font-size: 1.1rem; font-weight: 600;">Interactive Demo (Launching Soon)</h3>
                    </div>
                </div>
                <div style="margin-bottom: 1rem;">
                    <p style="font-size: 0.95rem; color: var(--color-text); line-height: 1.5; margin-bottom: 0.8rem;">
                        Try our conversational AI in a guided environment. Get a feel for how it responds, routes conversations, and handles both simple and complex tasks.
                    </p>
                </div>
            </div>

            <!-- Performance Dashboard -->
            <div class="ai-demo-card" style="background: rgba(255, 255, 255, 0.95); border-radius: 12px; padding: 1.8rem; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12); border: 1px solid rgba(245, 158, 11, 0.1); backdrop-filter: blur(10px);">
                <div style="display: flex; align-items: center; margin-bottom: 1.2rem;">
                    <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #f59e0b, #f97316); border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 1rem; box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);">
                        <span style="color: white; font-size: 18px;">📊</span>
                    </div>
                    <div>
                        <h3 style="margin: 0; color: var(--color-primary); font-size: 1.1rem; font-weight: 600;">Performance Dashboard (Preview Coming Soon)</h3>
                    </div>
                </div>
                <div style="margin-bottom: 1rem;">
                    <p style="font-size: 0.95rem; color: var(--color-text); line-height: 1.5; margin-bottom: 1rem;">
                        Track real-world efficiency metrics:
                    </p>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.8rem;">
                        <div style="text-align: center; padding: 0.8rem; background: rgba(79, 70, 229, 0.08); border-radius: 8px; border: 1px solid rgba(79, 70, 229, 0.2);">
                            <div style="font-size: 1.4rem; font-weight: bold; color: var(--color-primary);">65%</div>
                            <div style="font-size: 0.75rem; color: var(--color-text); opacity: 0.8;">Less CPU Usage</div>
                        </div>
                        <div style="text-align: center; padding: 0.8rem; background: rgba(16, 185, 129, 0.08); border-radius: 8px; border: 1px solid rgba(16, 185, 129, 0.2);">
                            <div style="font-size: 1.4rem; font-weight: bold; color: #10b981;">99.9%</div>
                            <div style="font-size: 0.75rem; color: var(--color-text); opacity: 0.8;">Uptime</div>
                        </div>
                        <div style="text-align: center; padding: 0.8rem; background: rgba(168, 85, 247, 0.08); border-radius: 8px; border: 1px solid rgba(168, 85, 247, 0.2);">
                            <div style="font-size: 1.4rem; font-weight: bold; color: #a855f7;">&lt;200ms</div>
                            <div style="font-size: 0.75rem; color: var(--color-text); opacity: 0.8;">Average Response Time</div>
                        </div>
                        <div style="text-align: center; padding: 0.8rem; background: rgba(245, 158, 11, 0.08); border-radius: 8px; border: 1px solid rgba(245, 158, 11, 0.2);">
                            <div style="font-size: 1.4rem; font-weight: bold; color: #f59e0b;">40+</div>
                            <div style="font-size: 0.75rem; color: var(--color-text); opacity: 0.8;">Supported Languages</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Integration Showcase -->
            <div class="ai-demo-card" style="background: rgba(255, 255, 255, 0.95); border-radius: 12px; padding: 1.8rem; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12); border: 1px solid rgba(139, 92, 246, 0.1); backdrop-filter: blur(10px);">
                <div style="display: flex; align-items: center; margin-bottom: 1.2rem;">
                    <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #8b5cf6, #a855f7); border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 1rem; box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);">
                        <span style="color: white; font-size: 18px;">🔌</span>
                    </div>
                    <div>
                        <h3 style="margin: 0; color: var(--color-primary); font-size: 1.1rem; font-weight: 600;">Integration Showcase (In Progress)</h3>
                    </div>
                </div>
                <div style="margin-bottom: 1rem;">
                    <p style="font-size: 0.95rem; color: var(--color-text); line-height: 1.5; margin-bottom: 1rem;">
                        Explore how Ziantrix connects with your tools:
                    </p>
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 0.8rem; margin-bottom: 1rem;">
                        <div style="text-align: center; padding: 0.6rem; background: rgba(79, 70, 229, 0.08); border-radius: 6px; border: 1px solid rgba(79, 70, 229, 0.2);">
                            <i class="fab fa-salesforce" style="font-size: 1.2rem; color: #00a1e0; margin-bottom: 0.3rem; display: block;"></i>
                            <div style="font-size: 0.7rem; color: var(--color-text);">Salesforce</div>
                        </div>
                        <div style="text-align: center; padding: 0.6rem; background: rgba(79, 70, 229, 0.08); border-radius: 6px; border: 1px solid rgba(79, 70, 229, 0.2);">
                            <i class="fab fa-slack" style="font-size: 1.2rem; color: #4a154b; margin-bottom: 0.3rem; display: block;"></i>
                            <div style="font-size: 0.7rem; color: var(--color-text);">Slack</div>
                        </div>
                        <div style="text-align: center; padding: 0.6rem; background: rgba(79, 70, 229, 0.08); border-radius: 6px; border: 1px solid rgba(79, 70, 229, 0.2);">
                            <i class="fab fa-shopify" style="font-size: 1.2rem; color: #96bf48; margin-bottom: 0.3rem; display: block;"></i>
                            <div style="font-size: 0.7rem; color: var(--color-text);">Shopify</div>
                        </div>
                    </div>
                    <div style="text-align: center; padding: 0.8rem; background: rgba(245, 158, 11, 0.08); border-radius: 8px; border: 1px solid rgba(245, 158, 11, 0.2);">
                        <div style="font-weight: 600; color: #f59e0b;">Custom APIs</div>
                        <div style="font-size: 0.75rem; color: var(--color-text); opacity: 0.8;">2–4 Weeks → Typical Setup Time</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Use Case Preview -->
        <div style="margin-top: 3rem; text-align: center; max-width: 900px; margin-left: auto; margin-right: auto;">
            <h3 style="color: var(--color-primary); margin-bottom: 2rem; font-size: 1.3rem; font-weight: 600; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">💼 Use Case Preview</h3>
            <p style="font-size: 1rem; color: var(--color-text); line-height: 1.6; margin-bottom: 2rem; background: rgba(255, 255, 255, 0.8); padding: 1rem; border-radius: 8px;">
                Here's how Ziantrix will handle industry-specific queries:
            </p>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: 1.2rem;">
                <div style="background: rgba(255, 255, 255, 0.95); padding: 1.2rem; border-radius: 10px; border-left: 3px solid #4f46e5; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08); text-align: left; backdrop-filter: blur(10px);">
                    <h4 style="color: var(--color-primary); margin-bottom: 0.5rem; font-size: 1rem; font-weight: 600;">E-commerce:</h4>
                    <p style="font-size: 0.85rem; color: var(--color-text); opacity: 0.8; margin-bottom: 0.5rem; font-style: italic;">"Track my order #12345"</p>
                    <p style="font-size: 0.8rem; color: #10b981; margin: 0;">→ Instant order status with shipping details</p>
                </div>
                <div style="background: rgba(255, 255, 255, 0.95); padding: 1.2rem; border-radius: 10px; border-left: 3px solid #10b981; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08); text-align: left; backdrop-filter: blur(10px);">
                    <h4 style="color: var(--color-primary); margin-bottom: 0.5rem; font-size: 1rem; font-weight: 600;">Healthcare:</h4>
                    <p style="font-size: 0.85rem; color: var(--color-text); opacity: 0.8; margin-bottom: 0.5rem; font-style: italic;">"Schedule my annual checkup"</p>
                    <p style="font-size: 0.8rem; color: #10b981; margin: 0;">→ HIPAA-compliant appointment booking</p>
                </div>
                <div style="background: rgba(255, 255, 255, 0.95); padding: 1.2rem; border-radius: 10px; border-left: 3px solid #a855f7; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08); text-align: left; backdrop-filter: blur(10px);">
                    <h4 style="color: var(--color-primary); margin-bottom: 0.5rem; font-size: 1rem; font-weight: 600;">Finance:</h4>
                    <p style="font-size: 0.85rem; color: var(--color-text); opacity: 0.8; margin-bottom: 0.5rem; font-style: italic;">"What's my account balance?"</p>
                    <p style="font-size: 0.8rem; color: #10b981; margin: 0;">→ Secure authentication and balance check</p>
                </div>
                <div style="background: rgba(255, 255, 255, 0.95); padding: 1.2rem; border-radius: 10px; border-left: 3px solid #f59e0b; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08); text-align: left; backdrop-filter: blur(10px);">
                    <h4 style="color: var(--color-primary); margin-bottom: 0.5rem; font-size: 1rem; font-weight: 600;">Education:</h4>
                    <p style="font-size: 0.85rem; color: var(--color-text); opacity: 0.8; margin-bottom: 0.5rem; font-style: italic;">"Help with calculus homework"</p>
                    <p style="font-size: 0.8rem; color: #10b981; margin: 0;">→ Step-by-step problem-solving guidance</p>
                </div>
            </div>
        </div>

        <!-- Early Access Section -->
        <div style="margin-top: 4rem; text-align: center; max-width: 600px; margin-left: auto; margin-right: auto;">
            <div style="background: linear-gradient(135deg, #4f46e5, #7c3aed) !important; padding: 2.5rem; border-radius: 16px; box-shadow: 0 8px 32px rgba(79, 70, 229, 0.3); border: 1px solid #4f46e5; backdrop-filter: blur(10px);">
                <h3 style="color: #ffffff !important; margin-bottom: 1rem; font-size: 1.4rem; font-weight: 600;">🚀 Want early access or a sneak peek?</h3>
                <p style="font-size: 1rem; color: #e2e8f0 !important; line-height: 1.6; margin-bottom: 2rem;">
                    Contact us to book a preview or demo session with our team.
                </p>
                <button onclick="openContactDemoModal()" class="cta-button">
                    Contact Us for Demo
                </button>
            </div>
        </div>
    </div>
</section>

<!-- Impact Section -->
<section id="impact" class="impact-section section-spacing" style="background: #f8f9fa; padding: 4rem 0;">
    <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 2rem;">
        <!-- Section Header -->
        <div class="section-header" style="text-align: left; margin-bottom: 3rem;">
            <h2 style="font-size: 2.5rem; font-weight: 700; color: #1a1a1a; margin-bottom: 0.75rem; letter-spacing: -0.02em;">What Our Clients Say</h2>
            <p style="font-size: 1.125rem; color: #6b7280; font-weight: 400;">Real results from real businesses</p>
        </div>

        <!-- Testimonials Container -->
        <div class="testimonials-container" style="position: relative; max-width: 700px; margin: 0 auto;">
            <!-- Testimonial Card -->
            <div class="testimonial-card" style="background: #ffffff !important; border-radius: 20px; padding: 3rem; box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1); border: 1px solid rgba(0, 0, 0, 0.05); position: relative;">

                <!-- Star Rating -->
                <div style="display: flex; gap: 0.5rem; margin-bottom: 2rem; justify-content: center;">
                    <span style="color: #fbbf24; font-size: 2.2rem;">⭐</span>
                    <span style="color: #fbbf24; font-size: 2.2rem;">⭐</span>
                    <span style="color: #fbbf24; font-size: 2.2rem;">⭐</span>
                    <span style="color: #fbbf24; font-size: 2.2rem;">⭐</span>
                    <span style="color: #fbbf24; font-size: 2.2rem;">⭐</span>
                </div>

                <!-- Quote Icon -->
                <div style="margin-bottom: 1.5rem; text-align: left;">
                    <span style="font-size: 2.5rem; color: #93c5fd !important; font-family: Georgia, serif; line-height: 1; font-weight: bold;">"</span>
                </div>

                <!-- Testimonial Text -->
                <blockquote class="testimonial-quote" style="font-size: 1.125rem; line-height: 1.6; color: #1f2937 !important; margin: 0 0 2.5rem 0; font-style: normal; font-weight: 500; text-align: left;">
                    Ziantrix's AI agent now resolves 85% of our HR-related queries instantly. It's taken a huge load off our team — they can finally focus on real problems.
                </blockquote>

                <!-- Author Info -->
                <div style="display: flex; align-items: center; gap: 1rem; margin-top: 2rem;">
                    <div class="testimonial-avatar" style="width: 48px !important; height: 48px !important; background: #4f46e5 !important; background: linear-gradient(135deg, #4f46e5, #7c3aed) !important; border-radius: 50% !important; display: flex !important; align-items: center !important; justify-content: center !important; color: #ffffff !important; font-weight: bold !important; font-size: 1rem !important; border: none !important; box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3) !important;">
                        SK
                    </div>
                    <div style="text-align: left;">
                        <div class="testimonial-author-name" style="font-weight: 600; color: #4f46e5 !important; font-size: 1rem; margin-bottom: 0.25rem;">Sanjeev Kumar</div>
                        <div class="testimonial-author-title" style="color: #64748b !important; font-size: 0.875rem; font-weight: 500;">CEO, Marketing Firm</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Arrows -->
        <div class="navigation-arrows" style="display: flex; justify-content: center; gap: 1rem; margin-top: 3rem;">
            <button onclick="previousTestimonial()" style="width: 50px !important; height: 50px !important; border-radius: 50% !important; border: 1px solid #e5e7eb !important; background: #ffffff !important; background-color: #ffffff !important; color: #6b7280 !important; cursor: pointer; display: flex !important; align-items: center !important; justify-content: center !important; transition: all 0.2s ease; font-size: 1.4rem !important; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;">
                ←
            </button>
            <button onclick="nextTestimonial()" style="width: 50px !important; height: 50px !important; border-radius: 50% !important; border: 1px solid #e5e7eb !important; background: #ffffff !important; background-color: #ffffff !important; color: #6b7280 !important; cursor: pointer; display: flex !important; align-items: center !important; justify-content: center !important; transition: all 0.2s ease; font-size: 1.4rem !important; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;">
                →
            </button>
        </div>

        <!-- JavaScript for testimonial rotation -->
        <script>
            const testimonials = [
                {
                    quote: "Ziantrix's AI agent now resolves 85% of our HR-related queries instantly. It's taken a huge load off our team — they can finally focus on real problems.",
                    author: "Sanjeev Kumar",
                    title: "CEO, Marketing Firm",
                    initials: "SK",
                    gradient: "linear-gradient(135deg, #4f46e5, #7c3aed)"
                },
                {
                    quote: "Ziantrix's AI chatbot now instantly answers routine medical queries. It's taken a huge load off our doctors — they can now focus on patients who need real care.",
                    author: "Dr. Raju",
                    title: "Consultant, Hospitals & Healthcare",
                    initials: "DR",
                    gradient: "linear-gradient(135deg, #f59e0b, #f97316)"
                },
                {
                    quote: "Ziantrix's AI agent now handles over 70% of our document-related queries and legal FAQs. It saves us hours every week and ensures faster, more consistent responses.",
                    author: "Kumara Swamy",
                    title: "Legal Advisor",
                    initials: "KS",
                    gradient: "linear-gradient(135deg, #06b6d4, #0891b2)"
                }
            ];

            let currentTestimonial = 0;

            function updateTestimonial() {
                const testimonial = testimonials[currentTestimonial];
                const quoteElement = document.querySelector('.testimonial-quote');
                const authorNameElement = document.querySelector('.testimonial-author-name');
                const authorTitleElement = document.querySelector('.testimonial-author-title');
                const avatarElement = document.querySelector('.testimonial-avatar');

                if (quoteElement) quoteElement.textContent = testimonial.quote;
                if (authorNameElement) authorNameElement.textContent = testimonial.author;
                if (authorTitleElement) authorTitleElement.textContent = testimonial.title;
                if (avatarElement) {
                    avatarElement.textContent = testimonial.initials;
                    avatarElement.style.setProperty('background', testimonial.gradient, 'important');
                    avatarElement.style.setProperty('color', '#ffffff', 'important');
                    avatarElement.style.setProperty('display', 'flex', 'important');
                    avatarElement.style.setProperty('align-items', 'center', 'important');
                    avatarElement.style.setProperty('justify-content', 'center', 'important');
                }
            }

            function nextTestimonial() {
                currentTestimonial = (currentTestimonial + 1) % testimonials.length;
                updateTestimonial();
            }

            function previousTestimonial() {
                currentTestimonial = (currentTestimonial - 1 + testimonials.length) % testimonials.length;
                updateTestimonial();
            }

            // Auto-rotate testimonials every 8 seconds
            setInterval(nextTestimonial, 8000);

            // Initialize first testimonial
            updateTestimonial();
        </script>

        <!-- Responsive CSS for mobile -->
        <style>
            @media (max-width: 768px) {
                #impact .container {
                    padding: 0 1rem !important;
                }

                #impact .section-header h2 {
                    font-size: 2rem !important;
                }

                #impact .section-header p {
                    font-size: 1rem !important;
                }

                #impact .testimonials-container {
                    max-width: 100% !important;
                }

                #impact .testimonial-card {
                    padding: 2rem !important;
                    border-radius: 16px !important;
                }

                #impact .testimonial-quote {
                    font-size: 1rem !important;
                }
            }

            @media (max-width: 480px) {
                #impact .section-header {
                    margin-bottom: 2rem !important;
                }

                #impact .section-header h2 {
                    font-size: 1.75rem !important;
                }

                #impact .testimonial-card {
                    padding: 1.5rem !important;
                    margin: 0 0.5rem !important;
                }

                #impact .testimonial-quote {
                    font-size: 0.95rem !important;
                    margin-bottom: 2rem !important;
                }

                #impact .navigation-arrows {
                    margin-top: 2.5rem !important;
                }

                #impact .navigation-arrows button {
                    width: 44px !important;
                    height: 44px !important;
                    font-size: 1rem !important;
                }
            }
        </style>
    </div>
</section>

<!-- Script for feature cards and checkmarks -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Function to fix service buttons
        function fixAllServiceButtons() {
            // Fix all service buttons
            const allButtons = document.querySelectorAll('.service-cta-button');
            allButtons.forEach(button => {
                button.style.width = '100%';
                button.style.maxWidth = '100%';
                button.style.minWidth = '0';
                button.style.boxSizing = 'border-box';
                button.style.display = 'flex';
                button.style.justifyContent = 'center';
                button.style.alignItems = 'center';
                button.style.padding = '14px 20px';
                button.style.textAlign = 'center';
                button.style.whiteSpace = 'normal';
                button.style.overflow = 'visible';
                button.style.textOverflow = 'clip';
                button.style.wordBreak = 'normal';
                button.style.wordWrap = 'normal';
                button.style.hyphens = 'none';
                button.style.lineHeight = '1.2';
                button.style.minHeight = '48px';
                button.style.height = 'auto';
                button.style.fontSize = '1rem';
            });

            // Fix all service cards
            const allCards = document.querySelectorAll('.service-card');
            allCards.forEach(card => {
                card.style.width = '100%';
                card.style.maxWidth = 'none';
                card.style.minWidth = '0';
                card.style.boxSizing = 'border-box';
                card.style.height = '520px';
                card.style.minHeight = '520px';
                card.style.maxHeight = '520px';
                card.style.display = 'flex';
                card.style.flexDirection = 'column';
                card.style.position = 'relative';
                card.style.paddingBottom = '120px';
            });

            // Fix all service card footers
            const allFooters = document.querySelectorAll('.service-card-footer');
            allFooters.forEach(footer => {
                footer.style.width = '100%';
                footer.style.maxWidth = '100%';
                footer.style.minWidth = '0';
                footer.style.boxSizing = 'border-box';
                footer.style.padding = '0 24px 24px';
                footer.style.position = 'absolute';
                footer.style.bottom = '30px';
                footer.style.left = '0';
                footer.style.right = '0';
            });
        }

        // Run the fix immediately and after delays
        fixAllServiceButtons();
        setTimeout(fixAllServiceButtons, 100);
        setTimeout(fixAllServiceButtons, 500);
        setTimeout(fixAllServiceButtons, 1000);

        // Run when DOM is loaded
        window.addEventListener('load', function() { // Use load instead of DOMContentLoaded here for redundancy
            fixAllServiceButtons();

            // Also run when window is resized
            window.addEventListener('resize', fixAllServiceButtons);

            // Add responsive styles for mobile
            if (window.innerWidth <= 768) {
                const servicesGrid = document.querySelector('.services-grid');
                if (servicesGrid) {
                    servicesGrid.style.gridTemplateColumns = '1fr !important';
                }
            } else if (window.innerWidth <= 992) {
                const servicesGrid = document.querySelector('.services-grid');
                if (servicesGrid) {
                    servicesGrid.style.gridTemplateColumns = 'repeat(2, 1fr) !important';
                }
            }

            // Function to remove backgrounds and align feature cards
            function fixFeatureCards() {
                // Target all feature metrics
                const metrics = document.querySelectorAll('.feature-metric, .feature-metric-label, .feature-proof-point');
                metrics.forEach(function(metric) {
                    metric.style.backgroundColor = 'transparent';
                    metric.style.background = 'transparent';
                    metric.style.boxShadow = 'none';
                    metric.style.border = 'none';
                    metric.style.padding = '0';
                    metric.style.textAlign = 'center';
                    metric.style.display = 'block';
                    metric.style.width = '100%';
                });

                // Target all feature cards
                const featureCards = document.querySelectorAll('.feature-card');
                featureCards.forEach(function(card) {
                    // Set consistent styling for all cards
                    card.style.display = 'flex';
                    card.style.flexDirection = 'column';
                    card.style.alignItems = 'center';
                    card.style.textAlign = 'center';
                    card.style.padding = '2rem';
                    card.style.border = '1px solid var(--color-border)';
                    card.style.borderRadius = '12px';
                    card.style.backgroundColor = 'transparent';
                    card.style.background = 'transparent';
                    card.style.height = '100%';
                    card.style.maxWidth = '360px';
                    card.style.margin = '0 auto';
                });
            }

            // Function to fix checkmark styling
            function fixCheckmarks() {
                // Target all checkmark icons
                const checkIcons = document.querySelectorAll('.check-icon');
                checkIcons.forEach(function(icon) {
                    // Ensure consistent styling
                    icon.style.color = 'var(--color-primary)';
                    icon.style.fontWeight = 'bold';
                    icon.style.marginRight = '0.75rem';
                    icon.style.flexShrink = '0';
                });

                // Fix any solution-benefits li elements that might have incorrect styling
                const benefitItems = document.querySelectorAll('.solution-benefits li');
                benefitItems.forEach(function(item) {
                    item.style.display = 'flex';
                    item.style.alignItems = 'flex-start';
                });
            }

            // Run immediately
            fixFeatureCards();
            fixCheckmarks();

            // Run after a short delay to ensure all styles are loaded
            setTimeout(fixFeatureCards, 100);
            setTimeout(fixCheckmarks, 100);

            // Run when theme changes
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                themeToggle.addEventListener('change', function() {
                    setTimeout(fixFeatureCards, 100);
                    setTimeout(fixCheckmarks, 100);
                    setTimeout(fixThirdButton, 100);
                });
            }
        }); // Closing bracket for window.addEventListener('load')
    });
</script>

<!-- FAQ Section (Resources) -->
<section id="faq" class="faq-section section-spacing">
    <div class="faq-container">
        <div class="section-header">
            <h2>Frequently Asked Questions</h2>
            <p>Get answers to common questions about our AI chatbot solutions</p>
        </div>



        <div class="faq-accordion">
            <div class="faq-item" data-keywords="security encryption data protection privacy gdpr ccpa">
                <button class="faq-question" aria-expanded="false">
                    <span class="faq-icon"><i class="fas fa-shield-alt"></i></span>
                    <span class="question-text">How secure is the Ziantrix chatbot?</span>
                    <span class="faq-toggle">
                        <i class="fas fa-angle-down"></i>
                    </span>
                </button>
                <div class="faq-answer" aria-hidden="true">
                    <div class="answer-content">
                        <p>Ziantrix employs enterprise-grade security measures including:</p>
                        <ul>
                            <li>End-to-end encryption for all conversations</li>
                            <li>SOC 2 Type II compliant data storage</li>
                            <li>Regular third-party security audits</li>
                            <li>Full compliance with GDPR, CCPA, and other data protection regulations</li>
                        </ul>
                        <p>We never store sensitive customer data without explicit permission, and all data is encrypted both in transit and at rest.</p>
                    </div>
                </div>
            </div>

            <div class="faq-item" data-keywords="customization branding personalization custom colors fonts visual design">
                <button class="faq-question" aria-expanded="false">
                    <span class="faq-icon"><i class="fas fa-paint-brush"></i></span>
                    <span class="question-text">Can the chatbot be customized to match our brand?</span>
                    <span class="faq-toggle">
                        <i class="fas fa-angle-down"></i>
                    </span>
                </button>
                <div class="faq-answer" aria-hidden="true">
                    <div class="answer-content">
                        <p>Absolutely! Our chatbots are fully customizable to match your brand identity:</p>
                        <ul>
                            <li>Custom colors, fonts, and visual elements</li>
                            <li>Personalized tone and voice to match your brand personality</li>
                            <li>Custom avatar or brand mascot integration</li>
                            <li>Tailored conversation flows specific to your business</li>
                        </ul>
                        <p>Our team works closely with you during implementation to ensure the chatbot represents your brand perfectly.</p>
                    </div>
                </div>
            </div>

            <div class="faq-item" data-keywords="implementation timeline deployment setup configuration training">
                <button class="faq-question" aria-expanded="false">
                    <span class="faq-icon"><i class="fas fa-clock"></i></span>
                    <span class="question-text">How long does implementation take?</span>
                    <span class="faq-toggle">
                        <i class="fas fa-angle-down"></i>
                    </span>
                </button>
                <div class="faq-answer" aria-hidden="true">
                    <div class="answer-content">
                        <h4>🔧 Implementation Timelines</h4>

                        <p>Our implementation process is tailored to your business needs:</p>

                        <div class="timeline-container">
                            <table class="implementation-table">
                                <tr>
                                    <th>Business Type</th>
                                    <th>Standard Setup</th>
                                    <th>Custom Solution</th>
                                </tr>
                                <tr>
                                    <td>Small & Medium Business</td>
                                    <td>3–5 days</td>
                                    <td>7–15 days</td>
                                </tr>
                                <tr>
                                    <td>Enterprise</td>
                                    <td>4–6 weeks</td>
                                    <td>8–12 weeks</td>
                                </tr>
                            </table>
                        </div>

                        <div class="timeline-details">
                            <div class="timeline-item">
                                <h5>Small & Medium Business</h5>
                                <p>Quick deployment with standard features, branding alignment, advanced security, and basic integrations.</p>
                            </div>

                            <div class="timeline-item">
                                <h5>Enterprise</h5>
                                <p>Comprehensive solutions with custom workflows, advanced security, and full system integration.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="faq-item" data-keywords="languages multilingual translation international global">
                <button class="faq-question" aria-expanded="false">
                    <span class="faq-icon"><i class="fas fa-globe"></i></span>
                    <span class="question-text">Does the chatbot support multiple languages?</span>
                    <span class="faq-toggle">
                        <i class="fas fa-angle-down"></i>
                    </span>
                </button>
                <div class="faq-answer" aria-hidden="true">
                    <div class="answer-content">
                        <p>Yes, our chatbot supports over 40 languages, including:</p>
                        <ul>
                            <li>English, Spanish, French, German, Italian</li>
                            <li>Chinese (Simplified & Traditional), Japanese, Korean</li>
                            <li>Arabic, Russian, Portuguese, Dutch</li>
                            <li>And many more regional languages</li>
                        </ul>
                        <p>The AI automatically detects the user's language and responds accordingly, making it perfect for global businesses.</p>
                    </div>
                </div>
            </div>

            <div class="faq-item" data-keywords="learning ai machine learning improvement training feedback">
                <button class="faq-question" aria-expanded="false">
                    <span class="faq-icon"><i class="fas fa-brain"></i></span>
                    <span class="question-text">How does the chatbot learn and improve over time?</span>
                    <span class="faq-toggle">
                        <i class="fas fa-angle-down"></i>
                    </span>
                </button>
                <div class="faq-answer" aria-hidden="true">
                    <div class="answer-content">
                        <p>Our chatbot uses advanced machine learning to continuously improve:</p>
                        <ul>
                            <li>Analyzes conversation patterns to identify common questions</li>
                            <li>Learns from successful human agent interactions</li>
                            <li>Improves response accuracy based on user feedback</li>
                            <li>Adapts to changing terminology and new products/services</li>
                        </ul>
                        <p>You have complete control over the learning process with our admin dashboard, where you can review and approve all learning updates.</p>
                    </div>
                </div>
            </div>

            <div class="faq-item" data-keywords="integration api crm zendesk salesforce shopify woocommerce">
                <button class="faq-question" aria-expanded="false">
                    <span class="faq-icon"><i class="fas fa-plug"></i></span>
                    <span class="question-text">Can Ziantrix integrate with our existing systems?</span>
                    <span class="faq-toggle">
                        <i class="fas fa-angle-down"></i>
                    </span>
                </button>
                <div class="faq-answer" aria-hidden="true">
                    <div class="answer-content">
                        <p>Yes, Ziantrix offers seamless integration with your existing tech stack:</p>
                        <ul>
                            <li>CRM systems (Salesforce, HubSpot, Zoho, etc.)</li>
                            <li>Help desk software (Zendesk, Freshdesk, ServiceNow)</li>
                            <li>E-commerce platforms (Shopify, WooCommerce, Magento)</li>
                            <li>Custom APIs and databases</li>
                        </ul>
                        <p>Our integration specialists ensure a smooth connection with minimal disruption to your current workflows.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Origin Story Section -->
<section id="origin-story" class="origin-story-section section-spacing">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Origin Story</h2>
        </div>
        
        <div class="origin-content">
            <div class="origin-text">
                <p>Ziantrix was created out of a clear gap: growing businesses drowning in routine support queries, with no affordable, scalable AI solution in sight.</p>
                <p>Instead of building yet another bot platform, we focused on something different — real automation that works on regular systems, deploys fast, and actually understands your internal workflows.</p>
                <p>No dev team. No GPU dependency. Just smart AI that starts delivering in days.</p>
                
                <div class="origin-stats">
                    <div class="stat-badge">
                        <span class="stat-icon">✅</span>
                        <span class="stat-text">Solo Founder</span>
                    </div>
                    <div class="stat-badge">
                        <span class="stat-icon">⚙️</span>
                        <span class="stat-text">&lt; 48 Hours to Deploy</span>
                    </div>
                    <div class="stat-badge">
                        <span class="stat-icon">🛠️</span>
                        <span class="stat-text">0 Developers Needed</span>
                    </div>
                </div>
            </div>
            
            <div class="origin-image">
                <img src="{{ url_for('static', filename='img/Vijay_image.jpg') }}" 
                     alt="Vijay Kumar Kodam - Founder" 
                     class="founder-image" 
                     loading="lazy" 
                     decoding="async" 
                     width="400" 
                     height="400"
                     style="aspect-ratio: 1/1;"
                     data-critical="true">
                <p class="founder-caption">Founder — Vijay Kumar Kodam</p>
            </div>
        </div>
    </div>
</section>

<style>
    .origin-story-section {
        background-color: #ffffff;
        padding: 80px 0;
    }

    .dark-theme .origin-story-section {
        background-color: #1a202c;
    }

    .origin-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 40px;
        align-items: center;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .origin-text {
        font-size: 1.1rem;
        line-height: 1.6;
        color: #374151;
    }

    .dark-theme .origin-text {
        color: #e2e8f0;
    }

    .origin-text p {
        margin-bottom: 1.5rem;
    }

    .origin-stats {
        display: flex;
        gap: 20px;
        margin-top: 2rem;
        flex-wrap: wrap;
    }

    .stat-badge {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background-color: #f3f4f6;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .dark-theme .stat-badge {
        background-color: #2d3748;
    }

    .stat-icon {
        font-size: 1.2rem;
    }

    .stat-text {
        font-weight: 500;
        color: #1f2937;
    }

    .dark-theme .stat-text {
        color: #e2e8f0;
    }

    .origin-image {
        text-align: center;
    }

    .founder-image {
        width: 100%;
        max-width: 400px;
        height: auto;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .founder-caption {
        margin-top: 1rem;
        font-size: 1rem;
        color: #6b7280;
    }

    .dark-theme .founder-caption {
        color: #a0aec0;
    }

    @media (max-width: 768px) {
        .origin-content {
            grid-template-columns: 1fr;
        }

        .origin-image {
            order: -1;
        }

        .founder-image {
            max-width: 300px;
        }

        .origin-stats {
            justify-content: center;
        }
    }
</style>

{% endblock %}

{% block scripts %}


<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Navigation highlighting
        const sections = document.querySelectorAll('section[id]');
        const navItems = document.querySelectorAll('.nav-item');

        // Highlight active nav item on scroll
        function highlightNavOnScroll() {
            let scrollPosition = window.scrollY;

            // Add offset for fixed header
            scrollPosition += 100; // Adjust based on header height

            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.offsetHeight;
                const sectionId = section.getAttribute('id');

                if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                    // Only highlight navigation items that are NOT in the footer
                    const headerNavItems = document.querySelectorAll('.navbar-link:not(footer .navbar-link), .dropdown-item:not(footer .dropdown-item)');
                    headerNavItems.forEach(item => {
                        item.classList.remove('active');
                        if (item.getAttribute('href') === `/#${sectionId}` ||
                            (item.getAttribute('href') === '/' && sectionId === 'hero')) {
                            item.classList.add('active');
                        }
                    });
                }
            });
        }

        // Initial call to highlight the correct nav item
        highlightNavOnScroll();

        // Add scroll event listener
        window.addEventListener('scroll', highlightNavOnScroll);

        // Add click event listeners to nav items
        navItems.forEach(item => {
            item.addEventListener('click', function(e) {
                // Skip for external links or javascript actions
                if (this.getAttribute('href').startsWith('javascript:') ||
                    !this.getAttribute('href').startsWith('/')) {
                    return;
                }

                const targetId = this.getAttribute('href').replace('/#', '');
                if (targetId === '/') return; // Skip for home link

                const targetSection = document.getElementById(targetId);
                if (targetSection) {
                    e.preventDefault();

                    // Remove active class from all nav items
                    navItems.forEach(navItem => navItem.classList.remove('active'));

                    // Add active class to clicked nav item
                    this.classList.add('active');

                    // Scroll to section
                    const headerOffset = 80; // Adjust based on header height
                    const elementPosition = targetSection.getBoundingClientRect().top;
                    const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                    window.scrollTo({
                        top: offsetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Counter animations
        const statNumbers = document.querySelectorAll('.stat-number[data-count]');
        let countersStarted = false;

        function startCounters() {
            if (countersStarted) return;

            statNumbers.forEach(stat => {
                const target = parseInt(stat.getAttribute('data-count'));
                const duration = 2000; // 2 seconds
                const startTime = Date.now();
                const startValue = 0;

                function updateCounter() {
                    const currentTime = Date.now();
                    const elapsedTime = currentTime - startTime;

                    if (elapsedTime < duration) {
                        const value = Math.floor((elapsedTime / duration) * target);
                        stat.textContent = value + '%';
                        requestAnimationFrame(updateCounter);
                    } else {
                        stat.textContent = target + '%';
                    }
                }

                updateCounter();
            });

            countersStarted = true;
        }

        // Start counters immediately and also when scrolled into view
        startCounters(); // Start immediately

        // Fallback: If counters are still at 0% after 1 second, force start them
        setTimeout(() => {
            const counterElements = document.querySelectorAll('.stat-number[data-count]');
            counterElements.forEach(el => {
                if (el.textContent === '0%') {
                    const target = parseInt(el.getAttribute('data-count'));
                    el.textContent = target + '%';
                }
            });
        }, 1000);

        // Also set up observer for when user scrolls to the section
        const aboutSection = document.querySelector('.about-section');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    startCounters();
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.2 });

        if (aboutSection) {
            observer.observe(aboutSection);
        }

        // No custom form validation needed - using browser's built-in validation

        // Initialize tooltip functionality
        const messageTooltip = document.getElementById('messageTooltip');
        if (messageTooltip) {
            messageTooltip.addEventListener('mouseenter', function() {
                this.querySelector('.tooltiptext').style.visibility = 'visible';
                this.querySelector('.tooltiptext').style.opacity = '1';
            });

            messageTooltip.addEventListener('mouseleave', function() {
                this.querySelector('.tooltiptext').style.visibility = 'hidden';
                this.querySelector('.tooltiptext').style.opacity = '0';
            });
        }

        // FAQ functionality
        const faqItems = document.querySelectorAll('.faq-item');

        faqItems.forEach(item => {
            const question = item.querySelector('.faq-question');

            question.addEventListener('click', () => {
                // Close all other items
                faqItems.forEach(otherItem => {
                    if (otherItem !== item && otherItem.classList.contains('active')) {
                        otherItem.classList.remove('active');
                    }
                });

                // Toggle current item
                item.classList.toggle('active');
            });
        });

        // Contact method click functionality
        const clickableContacts = document.querySelectorAll('.clickable-contact');

        clickableContacts.forEach(contact => {
            contact.addEventListener('click', function() {
                const contactType = this.getAttribute('data-contact-type');
                const contactValue = this.getAttribute('data-contact-value');

                switch(contactType) {
                    case 'email':
                        window.location.href = `mailto:${contactValue}`;
                        break;
                    case 'phone':
                        window.location.href = `tel:${contactValue}`;
                        break;
                    case 'linkedin':
                        window.open(contactValue, '_blank', 'noopener,noreferrer');
                        break;
                }
            });

            // Add cursor pointer style
            contact.style.cursor = 'pointer';
        });

    });
</script>



<!-- Service Features Alignment Fix -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Function to fix service features alignment
        function fixServiceFeaturesAlignment() {
            // Target all service cards
            const serviceCards = document.querySelectorAll('.service-card');
            serviceCards.forEach(function(card) {
                card.style.padding = '0';
                card.style.boxSizing = 'border-box';
            });

            // Target all service card bodies
            const serviceCardBodies = document.querySelectorAll('.service-card-body');
            serviceCardBodies.forEach(function(body) {
                body.style.padding = '0';
                body.style.paddingLeft = '0';
                body.style.paddingRight = '0';
                body.style.paddingTop = '8px';
                body.style.paddingBottom = '8px';
                body.style.margin = '0';
                body.style.width = '100%';
                body.style.boxSizing = 'border-box';
            });

            // Target all service features lists
            const featureLists = document.querySelectorAll('.service-features-list');
            featureLists.forEach(function(list) {
                list.style.padding = '0';
                list.style.paddingLeft = '0';
                list.style.paddingRight = '0';
                list.style.paddingTop = '0';
                list.style.paddingBottom = '0';
                list.style.margin = '0';
                list.style.marginLeft = '0';
                list.style.marginRight = '0';
                list.style.width = '100%';
                list.style.maxWidth = '100%';
                list.style.boxSizing = 'border-box';
            });

            // Target all service features list items
            const serviceFeatureItems = document.querySelectorAll('.service-features-list li'); // Renamed variable

            serviceFeatureItems.forEach(function(item) { // Using the renamed variable
                // Ensure flex display with row direction
                item.style.display = 'flex';
                item.style.flexDirection = 'row';
                item.style.alignItems = 'center';
                item.style.justifyContent = 'flex-start';
                item.style.textAlign = 'left';
                item.style.paddingLeft = '0';
                item.style.marginBottom = '0.75rem';

                // Fix icon alignment
                const icon = item.querySelector('.feature-icon');
                if (icon) {
                    icon.style.display = 'inline-flex';
                    icon.style.alignItems = 'center';
                    icon.style.justifyContent = 'center';
                    icon.style.verticalAlign = 'middle';
                    icon.style.marginRight = '0.75rem';
                    icon.style.float = 'left';
                    icon.style.background = 'none';
                    icon.style.backgroundColor = 'transparent';
                    icon.style.boxShadow = 'none';
                    icon.style.borderRadius = '0';
                }

                // Fix icon's inner elements
                const iconInner = item.querySelector('.feature-icon i');
                if (iconInner) {
                    iconInner.style.display = 'inline-block';
                    iconInner.style.verticalAlign = 'middle';
                    iconInner.style.background = 'none';
                    iconInner.style.backgroundColor = 'transparent';
                    iconInner.style.boxShadow = 'none';
                }

                // Fix text alignment
                const text = item.querySelector('.feature-text');
                if (text) {
                    text.style.display = 'inline-block';
                    text.style.verticalAlign = 'middle';
                    text.style.lineHeight = '1.4';
                    text.style.textAlign = 'left';
                }
            });
        }

        // Run immediately
        fixServiceFeaturesAlignment();

        // Also run after a short delay to ensure all styles are loaded
        setTimeout(fixServiceFeaturesAlignment, 100);
        setTimeout(fixServiceFeaturesAlignment, 500);

        // Run when theme changes
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('change', function() {
                setTimeout(fixServiceFeaturesAlignment, 100);
            });
        }

        // Run when window is resized
        window.addEventListener('resize', function() {
            setTimeout(fixServiceFeaturesAlignment, 100);
        });
    });
</script>
{% endblock %}
